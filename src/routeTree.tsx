import { createRootRoute, createRoute, createRouter, Outlet } from "@tanstack/react-router";

import "i18n";
import { Header } from "@components/layout/_header";
import { Sidebar } from "@components/layout/_sidebar";
import { ROUTE_PATH } from "@enums/route-path";
import { Index } from "@pages/index";
import { Settings } from "@pages/settings";
import type { ReactElement } from "react";

const RootComponent = (): ReactElement => (
  <div className="flex h-screen flex-col bg-accent-content">
    <Header />
    <div className="relative h-full flex-1 overflow-auto">
      <Outlet />
      <Sidebar />
    </div>
    {/* <Footer /> */}
  </div>
);

const rootRoute: ReturnType<typeof createRootRoute> = createRootRoute({
  component: RootComponent,
});

const routes = [
  { component: Index, path: ROUTE_PATH.HOME },
  { component: Index, path: ROUTE_PATH.TASKS },
  { component: Index, path: ROUTE_PATH.ALL_LEADS },
  { component: Settings, path: ROUTE_PATH.COLORS },
].map(({ path, component }) => createRoute({ component, getParentRoute: () => rootRoute, path }));

const routeTree = rootRoute.addChildren(routes);

export const router = createRouter({ routeTree });

declare module "@tanstack/react-router" {
  interface Register {
    router: typeof router;
  }
}
