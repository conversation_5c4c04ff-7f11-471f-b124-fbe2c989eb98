import { ROUTE_PATH } from "@enums/route-path";
import {
  faBarsProgress,
  faChevronRight,
  faGear,
  faHouse,
  faListUl,
} from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import type { Link } from "@tanstack/react-router";
import { cn } from "@utils/cn";

const MENU_ITEMS = [
  {
    activeClass: "[&.active]:bg-primary",
    icon: faHouse,
    label: "Home",
    path: ROUTE_PATH.HOME,
  },
  { icon: faBarsProgress, label: "All Leads" },
  { icon: faListUl, label: "Tasks" },
  { icon: faGear, label: "Settings" },
];

export const Sidebar = () => {
  return (
    <div className="absolute top-0 left-0 h-full py-2">
      <div className="menu-vertical size-10 h-full w-fit gap-2 rounded-r-lg border border-base-300 bg-base-100 p-2">
        <FontAwesomeIcon icon={faChevronRight} size="xl" className="mb-8 p-3" />
        {MENU_ITEMS.map(({ icon, label, activeClass, path }) => (
          <link to={path} key={label}>
            <FontAwesomeIcon
              key={label}
              icon={icon}
              size="xl"
              className={cn(
                "cursor-pointer rounded-lg p-3 hover:bg-primary-content/30",
                activeClass
              )}
            />
          </link>
        ))}
      </div>
    </div>
  );
};
